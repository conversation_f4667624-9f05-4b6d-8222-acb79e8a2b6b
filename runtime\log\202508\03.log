---------------------------------------------------------------

[2025-08-03T11:56:23+08:00] ********** GET localhost/manage
[ sql ] [ DB ] CONNECT:[ UseTime:0.002808s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001897s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.000311s ]
---------------------------------------------------------------

[2025-08-03T11:56:43+08:00] ********** POST localhost/manage/index/login
[ sql ] [ DB ] CONNECT:[ UseTime:0.000682s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000719s ]
[ sql ] [ SQL ] SELECT `manage_ip_white` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000282s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage` [ RunTime:0.010683s ]
[ sql ] [ SQL ] SELECT `id`,`username`,`password`,`safe_code` FROM `ly_manage` WHERE  `username` = 'admin'  AND `state` = 1 LIMIT 1 [ RunTime:0.000489s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_loginlog` [ RunTime:0.000942s ]
[ sql ] [ SQL ] INSERT INTO `ly_loginlog` (`uid` , `username` , `ip` , `address` , `os` , `time` , `browser` , `type` , `isadmin`) VALUES (24 , 'admin' , '**********' , '' , 'Windows 10' , 1754193403 , 'Chrome' , '后台网页版' , 1) [ RunTime:0.000374s ]
---------------------------------------------------------------

[2025-08-03T11:56:45+08:00] ********** GET localhost/manage/index
[ sql ] [ DB ] CONNECT:[ UseTime:0.000713s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000772s ]
[ sql ] [ SQL ] SELECT `manage_title` FROM `ly_setting` LIMIT 1 [ RunTime:0.000437s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.001048s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000530s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 1  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000731s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 2  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000408s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 4  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000281s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 5  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000391s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 3  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000356s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 331  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000349s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 347  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000397s ]
[ sql ] [ SQL ] SELECT * FROM `ly_manage_user_role` WHERE  `state` = 1  AND `level` = 2  AND `cid` = 363  AND `uid` = 24 ORDER BY `sort` ASC [ RunTime:0.000526s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.001478s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN ********** AND 1754193405 [ RunTime:0.000374s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `reg_time` BETWEEN 1754064000 AND 1754150399 [ RunTime:0.000197s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000991s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN ********** AND 1754193405 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `trade_type` = 9  AND `trade_time` BETWEEN 1754064000 AND 1754150399 [ RunTime:0.000613s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001281s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` [ RunTime:0.000216s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN ********** AND 1754193405 [ RunTime:0.000254s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_task` WHERE  `add_time` BETWEEN 1754064000 AND 1754150399 [ RunTime:0.000162s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.001160s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND 1754193405 [ RunTime:0.000419s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN 1754064000 AND 1754150399 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1 [ RunTime:0.000221s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN ********** AND 1754193405 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT SUM(`money`) AS tp_sum FROM `ly_user_recharge` WHERE  `state` = 1  AND `add_time` BETWEEN 1754064000 AND 1754150399 [ RunTime:0.000379s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000883s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000370s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND 1754193405 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN 1754064000 AND 1754150399 [ RunTime:0.000477s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1 [ RunTime:0.000247s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN ********** AND 1754193405 [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT SUM(`price`) AS tp_sum FROM `ly_user_withdrawals` WHERE  `state` = 1  AND `time` BETWEEN 1754064000 AND 1754150399 [ RunTime:0.000206s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000877s ]
[ sql ] [ SQL ] SELECT SUM(`balance`) AS tp_sum FROM `ly_user_total` [ RunTime:0.000393s ]
---------------------------------------------------------------

[2025-08-03T11:56:48+08:00] ********** GET localhost/
[ sql ] [ DB ] CONNECT:[ UseTime:0.000743s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000680s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000390s ]
---------------------------------------------------------------

[2025-08-03T11:56:50+08:00] ********** POST localhost/api/Common/GetLanguage
[ info ] BaseController action: getlanguage
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000917s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000917s ]
[ sql ] [ SQL ] SELECT `default_language` FROM `ly_setting` LIMIT 1 [ RunTime:0.000232s ]
---------------------------------------------------------------

[2025-08-03T11:56:50+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.005889s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.002132s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'cn' ORDER BY `add_time` DESC [ RunTime:0.000628s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.001622s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000834s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000854s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000411s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.001478s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000916s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.001245s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'cn' [ RunTime:0.001114s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000294s ]
---------------------------------------------------------------

[2025-08-03T11:56:50+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000975s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.002789s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 ) [ RunTime:0.003229s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task` WHERE  ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 ) ORDER BY `purchase_price` ASC LIMIT 0,10 [ RunTime:0.001575s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000709s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000563s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000500s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000376s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000379s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000318s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000302s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000307s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000395s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000511s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000484s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000359s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000264s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 28 LIMIT 1 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 2 LIMIT 1 [ RunTime:0.000278s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 28 LIMIT 1 [ RunTime:0.000270s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 2 LIMIT 1 [ RunTime:0.000437s ]
---------------------------------------------------------------

[2025-08-03T11:57:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000841s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193442 LIMIT 100 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000910s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000374s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000444s ]
---------------------------------------------------------------

[2025-08-03T11:58:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000845s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000865s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193504 LIMIT 100 [ RunTime:0.000603s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000821s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000385s ]
---------------------------------------------------------------

[2025-08-03T11:58:29+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
[ info ] 🔍 $_POST[token]: 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
[ info ] 🔍 $_REQUEST[token]: 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
[ info ] 🔍 最终user_token: 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
---------------------------------------------------------------

[2025-08-03T11:58:29+08:00] ********** POST localhost/api/Common/BackData
[ info ] BaseController action: backdata
[ info ] BaseController controller: Common
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.000672s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_notice` [ RunTime:0.000612s ]
[ sql ] [ SQL ] SELECT * FROM `ly_notice` WHERE  `state` = 1  AND `lang` = 'cn' ORDER BY `add_time` DESC [ RunTime:0.000385s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.001046s ]
[ sql ] [ SQL ] SELECT * FROM `ly_trade_details` WHERE  `trade_type` = 8 ORDER BY `id` DESC LIMIT 10 [ RunTime:0.000647s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000669s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `state` = 1 ORDER BY `num` ASC [ RunTime:0.000532s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000823s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` LIMIT 1 [ RunTime:0.000533s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000733s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `state` = 1  AND `is_hidden` = 0 ORDER BY `id` ASC [ RunTime:0.000398s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_slide` [ RunTime:0.000543s ]
[ sql ] [ SQL ] SELECT * FROM `ly_slide` WHERE  `status` = 1  AND `lang` = 'cn' [ RunTime:0.000269s ]
[ sql ] [ SQL ] SELECT `show_credit_interface` FROM `ly_setting` LIMIT 1 [ RunTime:0.000345s ]
---------------------------------------------------------------

[2025-08-03T11:58:29+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
[ info ] 🔍 $_POST[token]: 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
[ info ] 🔍 $_REQUEST[token]: 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
[ info ] 🔍 最终user_token: 39adJXMCVF+Qf+3s72ROFIgs2acKvxkjAPuCI4gm976yBlnT++Tpf+UHO3ugOA
---------------------------------------------------------------

[2025-08-03T11:58:37+08:00] ********** POST localhost/api/User/Login
[ info ] BaseController action: login
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ sql ] [ DB ] CONNECT:[ UseTime:0.001211s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000664s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `username` = '************'  AND ( state in(1,2) ) LIMIT 1 [ RunTime:0.000636s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_loginlog` [ RunTime:0.001146s ]
[ sql ] [ SQL ] INSERT INTO `ly_loginlog` (`uid` , `username` , `os` , `browser` , `ip` , `time` , `address` , `type`) VALUES (1156 , '************' , '未知操作系统' , '未知浏览器' , '**********' , 1754193517 , '' , '前台手机网页版') [ RunTime:0.000654s ]
[ sql ] [ SQL ] UPDATE `ly_users`  SET `last_ip` = '**********' , `last_login` = 1754193517 , `login_error` = 0 , `login_number` = `login_number` + 1  WHERE  `id` = 1156 [ RunTime:0.000614s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000799s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_total` WHERE  `uid` = 1156 LIMIT 1 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000936s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000523s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.001047s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1156  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000776s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1155  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000509s ]
---------------------------------------------------------------

[2025-08-03T11:58:37+08:00] ********** POST localhost/api/task/getTaskList
[ sql ] [ DB ] CONNECT:[ UseTime:0.000733s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000626s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1156 LIMIT 1 [ RunTime:0.002397s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` INNER JOIN `ly_user_grade` ON `ly_users`.`vip_level`=`ly_user_grade`.`grade` WHERE  `ly_users`.`id` = '1156' LIMIT 1 [ RunTime:0.000492s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000525s ]
[ sql ] [ SQL ] SELECT `task_id` FROM `ly_user_task` WHERE  `uid` = 1156  AND `add_time` >= **********  AND `add_time` <= ********** [ RunTime:0.000606s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task` [ RunTime:0.000560s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_task` WHERE  `uid` <> 1156  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') [ RunTime:0.002153s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task` WHERE  `uid` <> 1156  AND ( `surplus_number` > 0 )  AND ( `status` = 3 )  AND ( `end_time` >= ********** )  AND ( `is_visible` = 1 )  AND `ly_task`.`id` NOT IN ('') ORDER BY `purchase_price` ASC LIMIT 0,10 [ RunTime:0.001642s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_task_class` [ RunTime:0.000735s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000360s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000353s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000556s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000224s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000299s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000419s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000214s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000270s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000221s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000236s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000411s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 27 LIMIT 1 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000261s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 28 LIMIT 1 [ RunTime:0.000315s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 2 LIMIT 1 [ RunTime:0.000476s ]
[ sql ] [ SQL ] SELECT * FROM `ly_task_class` WHERE  `id` = 28 LIMIT 1 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 2 LIMIT 1 [ RunTime:0.000330s ]
---------------------------------------------------------------

[2025-08-03T11:58:39+08:00] ********** POST localhost/api/user/getStatisticsInfo
[ info ] BaseController action: getstatisticsinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000823s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000684s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000330s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.001291s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , 1754193519 , '[\"lang\",\"token\"]' , '[\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getstatisticsinfo' , 'User') [ RunTime:0.001850s ]
[ sql ] [ SQL ] SELECT * FROM `ly_users` WHERE  `id` = 1156 LIMIT 1 [ RunTime:0.000349s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000707s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000289s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000529s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754064000  AND `trade_time` <= 1754150399  AND `state` = 1 [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000371s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000309s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000278s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000649s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.001065s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1156  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000416s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000445s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000436s ]
---------------------------------------------------------------

[2025-08-03T11:58:40+08:00] ********** POST localhost/api/Account/getBankCardList
[ info ] BaseController action: getbankcardlist
[ info ] BaseController controller: Account
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.010051s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000614s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , ********** , '[\"lang\",\"token\"]' , '[\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getbankcardlist' , 'Account') [ RunTime:0.000338s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_bank` [ RunTime:0.001623s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_bank` WHERE  `uid` = 1156  AND `status` = 1 ORDER BY `id` DESC [ RunTime:0.000489s ]
---------------------------------------------------------------

[2025-08-03T11:58:40+08:00] ********** POST localhost/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000790s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000690s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000606s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , ********** , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"1\",\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000324s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000598s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1156 [ RunTime:0.000380s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1156 ORDER BY `add_time` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.001175s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.001183s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 120 LIMIT 1 [ RunTime:0.000455s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000214s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000147s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000225s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 120 LIMIT 1 [ RunTime:0.000160s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000183s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000172s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000332s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000215s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000201s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000137s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000141s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 120 LIMIT 1 [ RunTime:0.000243s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000250s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 120 LIMIT 1 [ RunTime:0.000286s ]
---------------------------------------------------------------

[2025-08-03T11:58:41+08:00] ********** POST localhost/api/Transaction/getRechargeRecord
[ info ] BaseController action: getrechargerecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000789s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000615s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000310s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000718s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , 1754193521 , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"2\",\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getrechargerecord' , 'Transaction') [ RunTime:0.000432s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_recharge` [ RunTime:0.000612s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_recharge` WHERE  `uid` = 1156 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_recharge` WHERE  `uid` = 1156 ORDER BY `add_time` DESC,`id` DESC LIMIT 10,10 [ RunTime:0.000305s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_bank` [ RunTime:0.000611s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000162s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_rechange_type` [ RunTime:0.000612s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 120 LIMIT 1 [ RunTime:0.000400s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000340s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000224s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000183s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000323s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000195s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000180s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000241s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000304s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000200s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 120 LIMIT 1 [ RunTime:0.000190s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000196s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000304s ]
[ sql ] [ SQL ] SELECT `bank_name` FROM `ly_bank` WHERE  `id` = 0 LIMIT 1 [ RunTime:0.000306s ]
[ sql ] [ SQL ] SELECT `name` FROM `ly_rechange_type` WHERE  `id` = 119 LIMIT 1 [ RunTime:0.000237s ]
---------------------------------------------------------------

[2025-08-03T11:58:42+08:00] ********** POST localhost/api/Transaction/getDrawRecord
[ info ] BaseController action: getdrawrecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000726s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000627s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000248s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000597s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , 1754193522 , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"1\",\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getdrawrecord' , 'Transaction') [ RunTime:0.000303s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000758s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `uid` = 1156 [ RunTime:0.000306s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `uid` = 1156 ORDER BY `time` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000377s ]
---------------------------------------------------------------

[2025-08-03T11:58:42+08:00] ********** POST localhost/api/Transaction/getDrawRecord
[ info ] BaseController action: getdrawrecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000825s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000593s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000446s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , 1754193522 , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"2\",\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getdrawrecord' , 'Transaction') [ RunTime:0.000389s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000541s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `uid` = 1156 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `uid` = 1156 ORDER BY `time` DESC,`id` DESC LIMIT 10,10 [ RunTime:0.000420s ]
---------------------------------------------------------------

[2025-08-03T11:58:50+08:00] ********** POST localhost/api/Transaction/draw
[ info ] BaseController action: draw
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000812s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000708s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000319s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000535s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , ********** , '[\"draw_type\",\"bank\",\"user_bank_id\",\"draw_money\",\"drawword\",\"ifsc\",\"lang\",\"token\"]' , '[\"bank\",\"Bank Maybank\",\"343\",\"5000\",\"123456\",\"\",\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'draw' , 'Transaction') [ RunTime:0.000206s ]
[ sql ] [ SQL ] SELECT `user_type` FROM `ly_users` WHERE  `id` = 1156 LIMIT 1 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SELECT `state` FROM `ly_users` WHERE  `id` = 1156 LIMIT 1 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `withdrawals_state` = 1 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT `credit` FROM `ly_users` WHERE  `id` = 1156 LIMIT 1 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000788s ]
[ sql ] [ SQL ] SELECT * FROM `ly_setting` WHERE  `id` = 1 LIMIT 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SELECT `fund_password` FROM `ly_users` WHERE  `id` = 1156 LIMIT 1 [ RunTime:0.000397s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000600s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1156 LIMIT 1 [ RunTime:0.000268s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_bank` [ RunTime:0.000487s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_bank` WHERE  `id` = 343 LIMIT 1 [ RunTime:0.000234s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1156 LIMIT 1 [ RunTime:0.000237s ]
[ sql ] [ SQL ] UPDATE `ly_user_total`  SET `balance` = `balance` - 5500  WHERE  `uid` = 1156  AND `balance` >= 5500 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000576s ]
[ sql ] [ SQL ] INSERT INTO `ly_user_withdrawals` (`uid` , `price` , `card_name` , `card_number` , `bank_id` , `bank_name` , `time` , `order_number` , `trade_number` , `fee` , `remarks`) VALUES (1156 , '5000' , '123' , '123465' , 7 , 'Bank Maybank' , ********** , '202508031158507923253049' , '202508031158507964958014' , 500 , '尊敬的用户您好！您的编号为202508031158507923253049 的提现处理中，金额￥5000元 服务费：￥500元，处理时间：2025-08-03 11:58:50') [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT `vip_level` FROM `ly_users` WHERE  `id` = 1156 LIMIT 1 [ RunTime:0.000261s ]
[ sql ] [ SQL ] SELECT `realname`,`username`,`vip_level`,`user_type` FROM `ly_users` WHERE  `id` = 1156 LIMIT 1 [ RunTime:0.000322s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000539s ]
[ sql ] [ SQL ] INSERT INTO `ly_trade_details` (`types` , `username` , `vip_level` , `user_type` , `uid` , `sid` , `source_uid` , `source_username` , `order_number` , `trade_number` , `trade_time` , `isadmin` , `trade_amount` , `trade_before_balance` , `account_balance` , `account_total_balance` , `remarks` , `remarks_en` , `remarks_id` , `remarks_ft` , `remarks_yd` , `remarks_vi` , `remarks_es` , `remarks_ja` , `remarks_th` , `remarks_ma` , `remarks_pt` , `state` , `trade_type`) VALUES (1 , '************' , 1 , 2 , 1156 , 1156 , 0 , '' , '202508031158507923253049' , '202508031158507964958014' , ********** , 2 , 5500 , '790499.00' , 784999 , 0 , '平台取款(含手续费￥500)' , 'Platform withdrawal (including fee $500)' , 'Penarikan platform (termasuk biaya Rp500)' , '平台取款(含手續費￥500)' , 'प्लेटफार्म निकासी (शुल्क ₹500 सहित)' , 'Rút tiền nền tảng (bao gồm phí 500đ)' , 'Retiro de plataforma (incluyendo tarifa €500)' , 'プラットフォーム出金(手数料¥500を含む)' , 'การถอนเงินแพลตฟอร์ม (รวมค่าธรรมเนียม ฿500)' , 'Pengeluaran platform (termasuk yuran RM500)' , 'Retirada da plataforma (incluindo taxa R$500)' , 3 , 2) [ RunTime:0.000484s ]
---------------------------------------------------------------

[2025-08-03T11:58:51+08:00] ********** POST localhost/api/Transaction/getDrawRecord
[ info ] BaseController action: getdrawrecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000775s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000632s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000572s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , 1754193531 , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"1\",\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getdrawrecord' , 'Transaction') [ RunTime:0.000329s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000601s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `uid` = 1156 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `uid` = 1156 ORDER BY `time` DESC,`id` DESC LIMIT 0,10 [ RunTime:0.000414s ]
---------------------------------------------------------------

[2025-08-03T11:58:51+08:00] ********** POST localhost/api/user/getUserInfo
[ info ] BaseController action: getuserinfo
[ info ] BaseController controller: User
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000683s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000512s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000432s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000684s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , 1754193531 , '[\"lang\",\"token\"]' , '[\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getuserinfo' , 'User') [ RunTime:0.000435s ]
[ sql ] [ SQL ] SELECT `u`.*,`t`.`balance`,`t`.`total_balance` FROM `ly_users` `u` INNER JOIN `ly_user_total` `t` ON `u`.`id`=`t`.`uid` WHERE  `u`.`id` = '1156' LIMIT 1 [ RunTime:0.000516s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_grade` [ RunTime:0.000994s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_grade` WHERE  `grade` = 1 LIMIT 1 [ RunTime:0.000469s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000985s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754064000  AND `trade_time` <= 1754150399  AND `state` = 1 [ RunTime:0.000457s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1754236800  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1753977600  AND `trade_time` <= 1756655999  AND `state` = 1 [ RunTime:0.000537s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `trade_time` >= 1751299200  AND `trade_time` <= 1753977599  AND `state` = 1 [ RunTime:0.000409s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` = 6  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000389s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_wheel_record` [ RunTime:0.000730s ]
[ sql ] [ SQL ] SELECT SUM(`num`) AS tp_sum FROM `ly_wheel_record` WHERE  `user_id` = 1156  AND `time` >= **********  AND `time` <= ********** [ RunTime:0.000498s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` = 19  AND `trade_time` >= **********  AND `trade_time` <= **********  AND `state` = 1 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SELECT SUM(`trade_amount`) AS tp_sum FROM `ly_trade_details` WHERE  `uid` = 1156  AND `trade_type` IN (5,6,7,8,10,15,16,17,18,21,22)  AND `state` = 1 [ RunTime:0.000250s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_vip` WHERE  `uid` = 1156  AND `state` = 1  AND `grade` = 1 LIMIT 1 [ RunTime:0.000331s ]
[ sql ] [ SQL ] SELECT `username` FROM `ly_users` WHERE  `id` = 1155  AND `state` = 1  AND `is_auto_f` = 1 LIMIT 1 [ RunTime:0.000423s ]
---------------------------------------------------------------

[2025-08-03T11:58:51+08:00] ********** POST localhost/api/Transaction/getDrawRecord
[ info ] BaseController action: getdrawrecord
[ info ] BaseController controller: Transaction
[ info ] BaseController module: api
[ info ] 处理HTTP请求加密数据
[ info ] 未检测到加密请求
[ info ] 🔍 Token获取调试:
[ info ] 🔍 input(post.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 input(get.token): NULL
[ info ] 🔍 input(param.token): 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_POST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 $_REQUEST[token]: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ info ] 🔍 最终user_token: 3101fgw52McXSn+qLd/tDnfAvEG3RXqk/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg
[ sql ] [ DB ] CONNECT:[ UseTime:0.000736s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_users` [ RunTime:0.000596s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_users` WHERE  `id` = 1156  AND `username` = '************'  AND `state` = 1 [ RunTime:0.000434s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_homelog` [ RunTime:0.000481s ]
[ sql ] [ SQL ] INSERT INTO `ly_homelog` (`uid` , `time` , `params` , `values` , `ip` , `func` , `cla`) VALUES (1156 , 1754193531 , '[\"state\",\"page_no\",\"lang\",\"token\"]' , '[\"0\",\"2\",\"cn\",\"3101fgw52McXSn+qLd\\/tDnfAvEG3RXqk\\/YpWHglXOxCMvlBnWP7zvbt1Fxgxxg\"]' , '**********' , 'getdrawrecord' , 'Transaction') [ RunTime:0.000345s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` WHERE  `uid` = 1156 [ RunTime:0.000278s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `uid` = 1156 ORDER BY `time` DESC,`id` DESC LIMIT 10,10 [ RunTime:0.000423s ]
---------------------------------------------------------------

[2025-08-03T11:59:02+08:00] ********** GET localhost/manage/Bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000973s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000588s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000416s ]
---------------------------------------------------------------

[2025-08-03T11:59:02+08:00] ********** GET localhost/manage/Bank/receivables
[ sql ] [ DB ] CONNECT:[ UseTime:0.000784s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000690s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/receivables'  AND `state` = 1 [ RunTime:0.000388s ]
---------------------------------------------------------------

[2025-08-03T11:59:02+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000668s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000750s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000502s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000861s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000697s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.003182s ]
---------------------------------------------------------------

[2025-08-03T11:59:02+08:00] ********** GET localhost/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.000860s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000622s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000398s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000592s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000336s ]
---------------------------------------------------------------

[2025-08-03T11:59:02+08:00] ********** POST localhost/manage/bank/receivables
[ sql ] [ DB ] CONNECT:[ UseTime:0.001565s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000601s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/receivables'  AND `state` = 1 [ RunTime:0.000373s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_recaivables` [ RunTime:0.001559s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_recaivables` [ RunTime:0.000179s ]
[ sql ] [ SQL ] SELECT `ly_recaivables`.*,`bank`.`bank_name`,rechange_type.name as rname FROM `ly_recaivables` LEFT JOIN `ly_bank` `bank` ON `ly_recaivables`.`bid`=`bank`.`id` LEFT JOIN `ly_rechange_type` `rechange_type` ON `ly_recaivables`.`type`=`rechange_type`.`id` ORDER BY `ly_recaivables`.`id` ASC LIMIT 0,10 [ RunTime:0.000440s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_vip` [ RunTime:0.000601s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_vip` [ RunTime:0.000350s ]
---------------------------------------------------------------

[2025-08-03T11:59:06+08:00] ********** GET localhost/manage/bank/controlAudit?id=332
[ sql ] [ DB ] CONNECT:[ UseTime:0.000633s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000454s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/controlaudit'  AND `state` = 1 [ RunTime:0.000298s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000554s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 332 LIMIT 1 [ RunTime:0.000282s ]
---------------------------------------------------------------

[2025-08-03T11:59:09+08:00] ********** POST localhost/manage/bank/controlAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000788s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000473s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/controlaudit'  AND `state` = 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000627s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `order_number` = '202508031158507923253049'  AND `state` IN (1,2,3) LIMIT 1 [ RunTime:0.000501s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `examine` = 1 , `remarks` = '尊敬的用户您好！您的编号为202508031158507923253049 的提现处理中，金额￥5000元 服务费：￥500元，处理时间：2025-08-03 11:58:50' , `aid` = 24 , `set_time` = **********  WHERE  `order_number` = '202508031158507923253049' [ RunTime:0.000257s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `aid` = 24 , `state` = 4 , `set_time` = ********** , `remarks` = '尊敬的用户您好！您的编号为202508031158507923253049 的提现处理中，金额￥5000元 服务费：￥500元，处理时间：2025-08-03 11:58:50'  WHERE  `id` = 332 [ RunTime:0.000388s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.000676s ]
[ sql ] [ SQL ] UPDATE `ly_trade_details`  SET `state` = 4 , `remarks` = '审核通过，等待支付'  WHERE  `order_number` = '202508031158507923253049' [ RunTime:0.000336s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.011072s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**********' , '审核订单号为202508031158507923253049的提现订单。处理状态：审核通过，等待支付' , 1) [ RunTime:0.000497s ]
---------------------------------------------------------------

[2025-08-03T11:59:10+08:00] ********** POST localhost/manage/bank/present_record
[ sql ] [ DB ] CONNECT:[ UseTime:0.000695s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000428s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/present_record'  AND `state` = 1 [ RunTime:0.000335s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000541s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid [ RunTime:0.000398s ]
[ sql ] [ SQL ] SELECT ly_user_withdrawals.*,manage.username as aname,users.username,danger,COALESCE(bank.bank_name, ub.bank_name) as bank_name,wc.name as channel_name FROM `ly_user_withdrawals` INNER JOIN `ly_users` `users` ON `ly_user_withdrawals`.`uid`=`users`.`id` LEFT JOIN `ly_manage` `manage` ON `ly_user_withdrawals`.`aid`=`manage`.`id` LEFT JOIN `ly_bank` `bank` ON `ly_user_withdrawals`.`bank_id`=`bank`.`id` LEFT JOIN `ly_user_bank` `ub` ON `ly_user_withdrawals`.`card_number`=ub.card_no AND ly_user_withdrawals.uid = ub.uid LEFT JOIN `ly_withdrawal_channel` `wc` ON `ly_user_withdrawals`.`channel_id`=`wc`.`id` ORDER BY `time` DESC LIMIT 0,10 [ RunTime:0.000910s ]
---------------------------------------------------------------

[2025-08-03T11:59:12+08:00] ********** GET localhost/manage/bank/controlAudit?id=332
[ sql ] [ DB ] CONNECT:[ UseTime:0.000741s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000681s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/controlaudit'  AND `state` = 1 [ RunTime:0.000512s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000610s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `id` = 332 LIMIT 1 [ RunTime:0.000304s ]
---------------------------------------------------------------

[2025-08-03T11:59:13+08:00] ********** GET localhost/manage/withdrawal_channel/getEnabledChannels
[ sql ] [ DB ] CONNECT:[ UseTime:0.000724s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000456s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'WithdrawalChannel/getenabledchannels'  AND `state` = 1 [ RunTime:0.000309s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000474s ]
[ sql ] [ SQL ] SELECT `id`,`name`,`code`,`mode`,`min_amount`,`max_amount`,`fee_rate` FROM `ly_withdrawal_channel` WHERE  `state` = 1 ORDER BY `sort` ASC [ RunTime:0.000269s ]
---------------------------------------------------------------

[2025-08-03T11:59:19+08:00] ********** POST localhost/manage/bank/executePayment
[ sql ] [ DB ] CONNECT:[ UseTime:0.000800s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_manage_user_role` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT COUNT(*) AS tp_count FROM `ly_manage_user_role` WHERE  `uid` = 24  AND `role_url` = 'Bank/executepayment'  AND `state` = 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_withdrawals` [ RunTime:0.000459s ]
[ sql ] [ SQL ] SELECT * FROM `ly_user_withdrawals` WHERE  `order_number` = '202508031158507923253049'  AND `state` = 4 LIMIT 1 [ RunTime:0.000242s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_withdrawal_channel` [ RunTime:0.000480s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 7  AND `state` = 1 LIMIT 1 [ RunTime:0.000320s ]
[ sql ] [ SQL ] SELECT * FROM `ly_withdrawal_channel` WHERE  `id` = 7 LIMIT 1 [ RunTime:0.000317s ]
[ sql ] [ SQL ] UPDATE `ly_user_withdrawals`  SET `aid` = 24 , `state` = 2 , `set_time` = ********** , `remarks` = '代付失败：MER_IP_ILLEGE，资金已退回'  WHERE  `id` = 332 [ RunTime:0.000419s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_total` [ RunTime:0.000622s ]
[ sql ] [ SQL ] SELECT `balance` FROM `ly_user_total` WHERE  `uid` = 1156 LIMIT 1 [ RunTime:0.000652s ]
[ sql ] [ SQL ] UPDATE `ly_user_total`  SET `balance` = `balance` + 5500  WHERE  `uid` = 1156 [ RunTime:0.000221s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_trade_details` [ RunTime:0.002765s ]
[ sql ] [ SQL ] UPDATE `ly_trade_details`  SET `state` = 2 , `remarks` = '代付失败：MER_IP_ILLEGE，资金已退回'  WHERE  `order_number` = '202508031158507923253049' [ RunTime:0.000205s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_actionlog` [ RunTime:0.000434s ]
[ sql ] [ SQL ] INSERT INTO `ly_actionlog` (`username` , `time` , `ip` , `log` , `isadmin`) VALUES ('admin' , ********** , '**********' , '执行订单号为202508031158507923253049的代付。处理状态：代付失败，资金已退回' , 1) [ RunTime:0.000225s ]
[ info ] WatchPay config loaded from file: {"enabled":true,"default_gateway":"https:\/\/api.watchglb.com","api_urls":{"pay":"https:\/\/api.watchglb.com\/pay\/web","transfer":"https:\/\/api.watchglb.com\/pay\/transfer","query_transfer":"https:\/\/api.watchglb.com\/query\/transfer","query_balance":"https:\/\/api.watchglb.com\/query\/balance"},"countries":{"ID":{"name":"\u5370\u5c3c","name_en":"Indonesia","currency":"IDR","merchant_id":"*********","pay_key":"YINGPTEMYQ7BAKOSHCAYZESK1WKU8XMK","withdrawal_key":"DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ","gateway_url":"https:\/\/api.watchglb.com","notify_domain":"https:\/\/www.lotteup.com","min_amount":20000,"max_amount":********,"enabled":true,"pay_types":{"220":{"name":"\u7f51\u94f6","type":"online","enabled":true,"fee_rate":0.045,"requires_bank_code":true,"min_amount":10000,"max_amount":********},"223":{"name":"\u626b\u7801","type":"scan","enabled":true,"fee_rate":0.055,"requires_bank_code":false,"min_amount":10000,"max_amount":********},"200":{"name":"\u7f51\u94f6B2C\u4e00\u7c7b","type":"online","enabled":false,"requires_bank_code":true},"201":{"name":"\u4fbf\u5229\u5e97\u4e00\u7c7b","type":"offline","enabled":false,"requires_bank_code":false},"202":{"name":"OVO\u94b1\u5305\u4e00\u7c7b","type":"wallet","enabled":false,"requires_bank_code":false},"203":{"name":"QRIS\u626b\u7801\u4e00\u7c7b","type":"scan","enabled":false,"requires_bank_code":false},"240":{"name":"\u7f51\u94f6B2C\u4e09\u7c7b","type":"online","enabled":false,"requires_bank_code":true},"243":{"name":"QRIS\u626b\u7801\u4e09\u7c7b","type":"scan","enabled":false,"requires_bank_code":false}},"supported_banks":{"BCA":{"name":"Bank Central Asia","name_id":"Bank Central Asia","enabled":true},"MANDIRI":{"name":"Bank Mandiri","name_id":"Bank Mandiri","enabled":true},"BNI":{"name":"Bank Negara Indonesia","name_id":"Bank Negara Indonesia","enabled":true},"BRI":{"name":"Bank Rakyat Indonesia","name_id":"Bank Rakyat Indonesia","enabled":true},"PERMATA":{"name":"Bank Permata","name_id":"Bank Permata","enabled":true},"CIMB":{"name":"Bank CIMB Niaga","name_id":"Bank CIMB Niaga","enabled":true},"MAYBANK":{"name":"Bank Maybank","name_id":"Bank Maybank","enabled":true},"DANAMON":{"name":"Bank Danamon","name_id":"Bank Danamon","enabled":true},"BSI":{"name":"Bank Syariah Indonesia","name_id":"Bank Syariah Indonesia","enabled":true},"BNC":{"name":"Neo Commerce","name_id":"Bank Yudha Bhakti","enabled":true}}}},"notify_ips":["*************"]}
[ info ] Found bank code for bank_id 7: Bank Maybank -> MAYBANK
[ info ] WatchPay sign debug - signStr: apply_date=2025-08-03 11:59:18&back_url=https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback&bank_code=MAYBANK&mch_id=*********&mch_transferId=202508031158507923253049&receive_account=123465&receive_name=123&transfer_amount=5000&key=DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ
[ info ] WatchPay sign debug - generated sign: cba9b226bdb999242329f237c49773cf
[ error ] WatchPay withdrawal direct failed: {"signType":null,"sign":null,"respCode":"FAIL","mchId":null,"merTransferId":null,"transferAmount":null,"applyDate":null,"tradeNo":null,"tradeResult":null,"errorMsg":"MER_IP_ILLEGE"}
---------------------------------------------------------------

[2025-08-03T11:59:25+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000823s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000531s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= ********** LIMIT 100 [ RunTime:0.000329s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000997s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000278s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000285s ]
---------------------------------------------------------------

[2025-08-03T12:00:26+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001154s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000947s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193625 LIMIT 100 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001203s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000306s ]
---------------------------------------------------------------

[2025-08-03T12:01:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.002443s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000608s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193686 LIMIT 100 [ RunTime:0.000312s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001742s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000291s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000316s ]
---------------------------------------------------------------

[2025-08-03T12:02:28+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001429s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000716s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193748 LIMIT 100 [ RunTime:0.000543s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000888s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000248s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000292s ]
---------------------------------------------------------------

[2025-08-03T12:03:29+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001033s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000623s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193809 LIMIT 100 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001036s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000288s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000536s ]
---------------------------------------------------------------

[2025-08-03T12:04:31+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001012s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000616s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193870 LIMIT 100 [ RunTime:0.000365s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001015s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000342s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000261s ]
---------------------------------------------------------------

[2025-08-03T12:05:33+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000940s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000803s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193932 LIMIT 100 [ RunTime:0.000385s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000912s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000381s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000351s ]
---------------------------------------------------------------

[2025-08-03T12:06:35+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000926s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000712s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754193995 LIMIT 100 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000805s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.001539s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000271s ]
---------------------------------------------------------------

[2025-08-03T12:07:38+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000887s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000550s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194057 LIMIT 100 [ RunTime:0.000280s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000859s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000278s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000276s ]
---------------------------------------------------------------

[2025-08-03T12:08:40+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000704s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000586s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194119 LIMIT 100 [ RunTime:0.000254s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000934s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000204s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000298s ]
---------------------------------------------------------------

[2025-08-03T12:09:41+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001580s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001173s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194181 LIMIT 100 [ RunTime:0.000413s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001115s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000389s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000416s ]
---------------------------------------------------------------

[2025-08-03T12:10:43+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001419s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000913s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194243 LIMIT 100 [ RunTime:0.000508s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000743s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000248s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000266s ]
---------------------------------------------------------------

[2025-08-03T12:11:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001156s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000585s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194304 LIMIT 100 [ RunTime:0.000238s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000911s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000421s ]
---------------------------------------------------------------

[2025-08-03T12:12:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001401s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000874s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194366 LIMIT 100 [ RunTime:0.000445s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000870s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000376s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000206s ]
---------------------------------------------------------------

[2025-08-03T12:13:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000903s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000475s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194429 LIMIT 100 [ RunTime:0.000264s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001010s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.002462s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000437s ]
---------------------------------------------------------------

[2025-08-03T12:14:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001405s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000886s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194492 LIMIT 100 [ RunTime:0.000468s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001206s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000294s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000353s ]
---------------------------------------------------------------

[2025-08-03T12:15:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000637s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000453s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194554 LIMIT 100 [ RunTime:0.000378s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001045s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000177s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000271s ]
---------------------------------------------------------------

[2025-08-03T12:16:57+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000784s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000521s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194616 LIMIT 100 [ RunTime:0.000276s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000831s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000228s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000216s ]
---------------------------------------------------------------

[2025-08-03T12:17:59+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001871s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000999s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194678 LIMIT 100 [ RunTime:0.000644s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001091s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000295s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000332s ]
---------------------------------------------------------------

[2025-08-03T12:19:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000964s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000811s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194741 LIMIT 100 [ RunTime:0.000481s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000780s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000194s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000358s ]
---------------------------------------------------------------

[2025-08-03T12:20:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001070s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000717s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194802 LIMIT 100 [ RunTime:0.000535s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000188s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000253s ]
---------------------------------------------------------------

[2025-08-03T12:21:04+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000834s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000467s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194864 LIMIT 100 [ RunTime:0.000388s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000804s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000287s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000547s ]
---------------------------------------------------------------

[2025-08-03T12:22:07+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000733s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000484s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194926 LIMIT 100 [ RunTime:0.000386s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000814s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000252s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000316s ]
---------------------------------------------------------------

[2025-08-03T12:23:08+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001100s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000750s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754194988 LIMIT 100 [ RunTime:0.000511s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001127s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000513s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000549s ]
---------------------------------------------------------------

[2025-08-03T12:24:11+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000867s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000462s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195050 LIMIT 100 [ RunTime:0.000248s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000965s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000245s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000391s ]
---------------------------------------------------------------

[2025-08-03T12:25:14+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000773s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000458s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195113 LIMIT 100 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001069s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000310s ]
---------------------------------------------------------------

[2025-08-03T12:26:15+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000978s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000548s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195175 LIMIT 100 [ RunTime:0.000342s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000801s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000375s ]
---------------------------------------------------------------

[2025-08-03T12:27:18+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000935s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000551s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195237 LIMIT 100 [ RunTime:0.000421s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000888s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000391s ]
---------------------------------------------------------------

[2025-08-03T12:28:19+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000999s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000593s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195299 LIMIT 100 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001642s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000558s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000584s ]
---------------------------------------------------------------

[2025-08-03T12:29:21+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000838s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000575s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195361 LIMIT 100 [ RunTime:0.000314s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000967s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000278s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000373s ]
---------------------------------------------------------------

[2025-08-03T12:30:22+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001004s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000795s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195422 LIMIT 100 [ RunTime:0.000423s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001131s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000447s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000371s ]
---------------------------------------------------------------

[2025-08-03T12:31:23+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000877s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000730s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195483 LIMIT 100 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001049s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000218s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000277s ]
---------------------------------------------------------------

[2025-08-03T12:32:26+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001048s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000468s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195545 LIMIT 100 [ RunTime:0.000246s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.002200s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000416s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000460s ]
---------------------------------------------------------------

[2025-08-03T12:33:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000793s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000625s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195606 LIMIT 100 [ RunTime:0.000367s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000818s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000275s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000270s ]
---------------------------------------------------------------

[2025-08-03T12:34:29+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001018s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000498s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195668 LIMIT 100 [ RunTime:0.000355s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000951s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000366s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000391s ]
---------------------------------------------------------------

[2025-08-03T12:35:30+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001288s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000642s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195730 LIMIT 100 [ RunTime:0.000317s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000978s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000347s ]
---------------------------------------------------------------

[2025-08-03T12:36:33+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001022s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000505s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195792 LIMIT 100 [ RunTime:0.000256s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000806s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000376s ]
---------------------------------------------------------------

[2025-08-03T12:37:35+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000856s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000457s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195855 LIMIT 100 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000770s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000326s ]
---------------------------------------------------------------

[2025-08-03T12:38:37+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001086s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000646s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195917 LIMIT 100 [ RunTime:0.000292s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000868s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000222s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000241s ]
---------------------------------------------------------------

[2025-08-03T12:39:38+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001115s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000588s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754195978 LIMIT 100 [ RunTime:0.000498s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001104s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000352s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000440s ]
---------------------------------------------------------------

[2025-08-03T12:40:40+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000881s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000601s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196040 LIMIT 100 [ RunTime:0.000338s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000902s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000262s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000346s ]
---------------------------------------------------------------

[2025-08-03T12:41:42+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000982s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000593s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196102 LIMIT 100 [ RunTime:0.000249s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000796s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000371s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.001063s ]
---------------------------------------------------------------

[2025-08-03T12:42:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001103s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000506s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196163 LIMIT 100 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001097s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000288s ]
---------------------------------------------------------------

[2025-08-03T12:43:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001158s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196225 LIMIT 100 [ RunTime:0.000277s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000965s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000206s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000478s ]
---------------------------------------------------------------

[2025-08-03T12:44:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000950s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000484s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196287 LIMIT 100 [ RunTime:0.000315s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001197s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000310s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000426s ]
---------------------------------------------------------------

[2025-08-03T12:45:49+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000750s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000499s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196348 LIMIT 100 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001105s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000415s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000243s ]
---------------------------------------------------------------

[2025-08-03T12:46:52+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000901s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000477s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196412 LIMIT 100 [ RunTime:0.000255s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000713s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000321s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000399s ]
---------------------------------------------------------------

[2025-08-03T12:47:53+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001429s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001481s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196473 LIMIT 100 [ RunTime:0.000343s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001009s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000358s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000377s ]
---------------------------------------------------------------

[2025-08-03T12:48:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001480s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000768s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196534 LIMIT 100 [ RunTime:0.000417s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000806s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000231s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000297s ]
---------------------------------------------------------------

[2025-08-03T12:49:58+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000947s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000723s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196597 LIMIT 100 [ RunTime:0.000305s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000714s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000225s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.001499s ]
---------------------------------------------------------------

[2025-08-03T12:51:01+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000948s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000678s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196660 LIMIT 100 [ RunTime:0.000261s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001062s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000334s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000434s ]
---------------------------------------------------------------

[2025-08-03T12:52:03+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000882s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000674s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196723 LIMIT 100 [ RunTime:0.000372s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000983s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000218s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000379s ]
---------------------------------------------------------------

[2025-08-03T12:53:06+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000964s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000901s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196785 LIMIT 100 [ RunTime:0.000302s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000823s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000280s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000360s ]
---------------------------------------------------------------

[2025-08-03T12:54:08+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.003009s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000545s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196847 LIMIT 100 [ RunTime:0.000292s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000953s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000320s ]
---------------------------------------------------------------

[2025-08-03T12:55:10+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001427s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001063s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196910 LIMIT 100 [ RunTime:0.000555s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000836s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000279s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000310s ]
---------------------------------------------------------------

[2025-08-03T12:56:12+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001328s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.001095s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754196972 LIMIT 100 [ RunTime:0.000383s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001069s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000259s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000302s ]
---------------------------------------------------------------

[2025-08-03T12:57:15+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001196s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000870s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197034 LIMIT 100 [ RunTime:0.000465s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001171s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000351s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000630s ]
---------------------------------------------------------------

[2025-08-03T12:58:17+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000940s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000599s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197096 LIMIT 100 [ RunTime:0.000319s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000664s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000188s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000251s ]
---------------------------------------------------------------

[2025-08-03T12:59:19+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000866s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000491s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197158 LIMIT 100 [ RunTime:0.000267s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000923s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000257s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000264s ]
---------------------------------------------------------------

[2025-08-03T13:00:20+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001053s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000493s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197220 LIMIT 100 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001062s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000252s ]
---------------------------------------------------------------

[2025-08-03T13:01:22+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001066s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000730s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197282 LIMIT 100 [ RunTime:0.000293s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001027s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000236s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000451s ]
---------------------------------------------------------------

[2025-08-03T13:02:24+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000886s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000519s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197343 LIMIT 100 [ RunTime:0.000296s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000699s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000219s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000252s ]
---------------------------------------------------------------

[2025-08-03T13:03:26+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001113s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197405 LIMIT 100 [ RunTime:0.000233s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000978s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000229s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000390s ]
---------------------------------------------------------------

[2025-08-03T13:04:27+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000875s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000549s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197467 LIMIT 100 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000812s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000221s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000252s ]
---------------------------------------------------------------

[2025-08-03T13:05:28+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001080s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000778s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197528 LIMIT 100 [ RunTime:0.000403s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001015s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000270s ]
---------------------------------------------------------------

[2025-08-03T13:06:30+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001068s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000528s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197589 LIMIT 100 [ RunTime:0.000266s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000884s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000263s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000310s ]
---------------------------------------------------------------

[2025-08-03T13:07:31+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000972s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000778s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197651 LIMIT 100 [ RunTime:0.000290s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000853s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000240s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000611s ]
---------------------------------------------------------------

[2025-08-03T13:08:32+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000941s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000541s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197712 LIMIT 100 [ RunTime:0.000314s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000957s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000284s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000358s ]
---------------------------------------------------------------

[2025-08-03T13:09:34+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001079s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000642s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197773 LIMIT 100 [ RunTime:0.000216s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000846s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000485s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000347s ]
---------------------------------------------------------------

[2025-08-03T13:10:35+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000901s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000450s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197835 LIMIT 100 [ RunTime:0.000250s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000847s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000197s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000211s ]
---------------------------------------------------------------

[2025-08-03T13:11:36+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000821s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000501s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197896 LIMIT 100 [ RunTime:0.000269s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000900s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000376s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000425s ]
---------------------------------------------------------------

[2025-08-03T13:12:37+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001269s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000795s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754197957 LIMIT 100 [ RunTime:0.000281s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001046s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000333s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000271s ]
---------------------------------------------------------------

[2025-08-03T13:13:38+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001366s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000679s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198018 LIMIT 100 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000774s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000316s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000476s ]
---------------------------------------------------------------

[2025-08-03T13:14:39+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000948s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000574s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198079 LIMIT 100 [ RunTime:0.000301s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000894s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000319s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000260s ]
---------------------------------------------------------------

[2025-08-03T13:15:40+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001602s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000877s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198139 LIMIT 100 [ RunTime:0.000393s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001229s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000348s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000492s ]
---------------------------------------------------------------

[2025-08-03T13:16:40+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000942s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000568s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198200 LIMIT 100 [ RunTime:0.000361s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000889s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000254s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000280s ]
---------------------------------------------------------------

[2025-08-03T13:17:42+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001095s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000825s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198262 LIMIT 100 [ RunTime:0.000258s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000946s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000208s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000224s ]
---------------------------------------------------------------

[2025-08-03T13:18:43+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001008s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000691s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198323 LIMIT 100 [ RunTime:0.000688s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001132s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000232s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000325s ]
---------------------------------------------------------------

[2025-08-03T13:19:44+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000833s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000567s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198384 LIMIT 100 [ RunTime:0.000253s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000828s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000304s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000280s ]
---------------------------------------------------------------

[2025-08-03T13:20:45+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000903s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000580s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198445 LIMIT 100 [ RunTime:0.000247s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000792s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000368s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000367s ]
---------------------------------------------------------------

[2025-08-03T13:21:47+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001344s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000644s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198506 LIMIT 100 [ RunTime:0.000389s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000848s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000485s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000314s ]
---------------------------------------------------------------

[2025-08-03T13:22:50+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000834s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000481s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198569 LIMIT 100 [ RunTime:0.000316s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001063s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000313s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000328s ]
---------------------------------------------------------------

[2025-08-03T13:23:51+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001180s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000636s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198631 LIMIT 100 [ RunTime:0.000344s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000867s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000230s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000397s ]
---------------------------------------------------------------

[2025-08-03T13:24:53+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000901s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000488s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198692 LIMIT 100 [ RunTime:0.000337s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000805s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000390s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000360s ]
---------------------------------------------------------------

[2025-08-03T13:25:55+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001008s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000533s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198754 LIMIT 100 [ RunTime:0.000340s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000805s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000402s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000446s ]
---------------------------------------------------------------

[2025-08-03T13:26:57+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001098s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000531s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198817 LIMIT 100 [ RunTime:0.000239s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001068s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000251s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000305s ]
---------------------------------------------------------------

[2025-08-03T13:27:59+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001052s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000492s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198879 LIMIT 100 [ RunTime:0.000541s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000899s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000299s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000286s ]
---------------------------------------------------------------

[2025-08-03T13:29:02+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001036s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000548s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754198941 LIMIT 100 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000987s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000260s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000272s ]
---------------------------------------------------------------

[2025-08-03T13:30:05+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001080s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000649s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754199004 LIMIT 100 [ RunTime:0.000363s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000977s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000336s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000231s ]
---------------------------------------------------------------

[2025-08-03T13:31:06+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.000801s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000566s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754199066 LIMIT 100 [ RunTime:0.000346s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000780s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000224s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000474s ]
---------------------------------------------------------------

[2025-08-03T13:32:07+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001087s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000577s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754199127 LIMIT 100 [ RunTime:0.000340s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.001162s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000420s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000536s ]
---------------------------------------------------------------

[2025-08-03T13:33:08+08:00] ********** GET nginx/create/index/autoAudit
[ sql ] [ DB ] CONNECT:[ UseTime:0.001335s ] mysql:host=mysql;dbname=dianzhan;charset=utf8
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_user_task` [ RunTime:0.000903s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`task_id`,`ly_user_task`.`uid` FROM `ly_user_task` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` > 0  AND `ly_user_task`.`auto_audit_time` <= 1754199188 LIMIT 100 [ RunTime:0.000344s ]
[ sql ] [ SQL ] SHOW COLUMNS FROM `ly_setting` [ RunTime:0.000879s ]
[ sql ] [ SQL ] SELECT `auto_audit` FROM `ly_setting` WHERE  `id` > 0 LIMIT 1 [ RunTime:0.000222s ]
[ sql ] [ SQL ] SELECT `ly_user_task`.`id`,`ly_user_task`.`examine_demo`,`task_id` FROM `ly_user_task` INNER JOIN `ly_task` ON `ly_user_task`.`task_id`=`ly_task`.`id` WHERE  `ly_user_task`.`status` = 2  AND `ly_user_task`.`auto_audit_time` = 0  AND `trial_time` >= 1754064000 LIMIT 600 [ RunTime:0.000251s ]
